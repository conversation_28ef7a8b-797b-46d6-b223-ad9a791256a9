const config = require('./config');
const MAX_MAILS = config.MAX_MAILS || 50;
const EXPIRE_MS = (config.MAIL_EXPIRE_MINUTES || 10) * 60 * 1000; // 10分钟
const store = new Map(); // mailboxAddr -> [mail, ...]

// 添加时间戳格式化函数
function getTimestamp() {
  return new Date().toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

function saveMail(mailboxAddr, mail) {
  mail._ts = Date.now(); // 增加时间戳
  console.log(`[${getTimestamp()}] 邮件时间戳设置: ${mail._ts}, 当前时间: ${Date.now()}, 主题: ${mail.subject}`);
  if (!store.has(mailboxAddr)) {
    store.set(mailboxAddr, []);
  }
  const arr = store.get(mailboxAddr);
  arr.unshift(mail);
  if (arr.length > MAX_MAILS) arr.length = MAX_MAILS;
}

function getMailsByMailbox(mailboxAddr) {
  const now = Date.now();
  const arr = store.get(mailboxAddr) || [];
  console.log(`[${getTimestamp()}] 获取邮件列表: 邮箱=${mailboxAddr}, 总邮件数=${arr.length}, 当前时间=${now}, 过期时间=${EXPIRE_MS}ms`);
  // 只返回未过期的邮件
  const validMails = arr.filter(mail => {
    const age = now - (mail._ts || 0);
    const isValid = age < EXPIRE_MS;
    if (!isValid) {
      console.log(`[${getTimestamp()}] 邮件已过期: 主题="${mail.subject}", 年龄=${age}ms, 过期阈值=${EXPIRE_MS}ms`);
    }
    return isValid;
  });
  console.log(`[${getTimestamp()}] 返回有效邮件数: ${validMails.length}`);
  return validMails;
}

function getMailByIdx(mailboxAddr, idx) {
  const now = Date.now();
  const arr = store.get(mailboxAddr) || [];
  // 获取指定索引的邮件，确保邮件未过期
  const mail = arr[idx];
  if (mail && (now - (mail._ts || 0) < EXPIRE_MS)) {
    return mail;
  }
  return null;
}

function deleteMail(mailboxAddr, idx) {
  const arr = store.get(mailboxAddr);
  if (arr && arr[idx] !== undefined) {
    arr.splice(idx, 1);
    return true;
  }
  return false;
}

// 定时清理过期邮件
setInterval(() => {
  const now = Date.now();
  let totalCleaned = 0;
  for (const [id, arr] of store.entries()) {
    const originalLength = arr.length;
    const filteredArr = arr.filter(mail => now - (mail._ts || 0) < EXPIRE_MS);
    const cleanedCount = originalLength - filteredArr.length;
    if (cleanedCount > 0) {
      console.log(`[${getTimestamp()}] 清理过期邮件: 邮箱 ${id} 清理了 ${cleanedCount} 封邮件`);
      totalCleaned += cleanedCount;
    }
    store.set(id, filteredArr);
  }
  if (totalCleaned > 0) {
    console.log(`[${getTimestamp()}] 本次清理总计: ${totalCleaned} 封过期邮件，过期时间: ${config.MAIL_EXPIRE_MINUTES} 分钟`);
  }
}, 60 * 1000); // 每分钟清理一次

module.exports = {
  saveMail,
  getMailsByMailbox,
  getMailByIdx,
  deleteMail
};