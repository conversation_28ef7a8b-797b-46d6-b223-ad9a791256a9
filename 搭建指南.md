# MeteorMail 临时邮箱搭建指南

## 项目简介

MeteorMail 是一个基于 Node.js 的自托管临时邮箱服务，支持以下功能：
- WebSocket 实时推送新邮件
- API 查询邮件
- 邮件自动过期
- 手动删除邮件
- 美观的前端界面（支持深色模式）
- 管理后台（带登录保护）
- 邮箱前缀黑名单

## 更新日志

### 2025-06-21 更新
- **新增管理后台**：添加了带登录保护的管理页面
  - 动态设置邮件保留时效
  - 动态设置每个邮箱保留的邮件数量
  - 设置禁用的邮箱前缀列表（如 `admin`, `root`）
- **安全增强**：禁止使用特定前缀创建邮箱，防止敏感邮箱被滥用
- **多语言支持**：管理界面支持中英文切换
- **深色模式**：管理界面支持深色/浅色主题切换
- **Docker 镜像升级**：支持 ARM64 和 AMD64 多平台架构

## 环境要求

- Node.js 16+ (推荐LTS版本)
- 推荐系统：CentOS/Ubuntu/Debian 或 macOS
- 需要开放的端口：
  - Web服务端口 (默认80)
  - SMTP服务端口 (默认25)

## Node.js 和 npm 安装指南

### 在 Ubuntu/Debian 上安装

```bash
# 更新软件包列表
sudo apt update

# 安装Node.js和npm
sudo apt install nodejs npm

# 安装 n 模块来管理Node.js版本
sudo npm install -g n

# 安装最新的LTS版本
sudo n lts

# 验证安装
node -v  # 应显示v16.x.x或更高版本
npm -v   # 应显示v8.x.x或更高版本
```

### 在 CentOS/RHEL 上安装

```bash
# 添加NodeSource仓库
sudo curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -

# 安装Node.js和npm
sudo yum install -y nodejs

# 验证安装
node -v
npm -v
```

### 在 macOS 上安装

使用Homebrew安装:

```bash
# 安装Homebrew(如果尚未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Node.js(包含npm)
brew install node

# 验证安装
node -v
npm -v
```

或使用nvm（Node版本管理器）:

```bash
# 安装nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash

# 重新加载shell配置
source ~/.bashrc  # 或 source ~/.zshrc

# 安装最新的LTS版本
nvm install --lts

# 设置为默认版本
nvm use --lts

# 验证安装
node -v
npm -v
```

## 快速搭建

### 1. 获取源码

```bash
# 克隆仓库
git clone https://github.com/lbjlaq/MeteorMail.git
cd MeteorMail
```

### 2. 安装依赖

```bash
# 安装项目依赖
npm install
```

### 3. 配置服务

项目支持两种配置方式：
1. **环境变量配置（.env文件）** - 优先级更高
2. **JSON配置（config.json文件）** - 作为默认配置

#### 使用 .env 文件配置（推荐）

在项目根目录下创建 `.env` 文件：
```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

.env 文件示例：
```
PORT=80
SMTP_PORT=25
SMTP_HOST=0.0.0.0
MAX_MAILS=50
MAIL_EXPIRE_MINUTES=10
ADMIN_USER=admin
ADMIN_PASSWORD=password
FORBIDDEN_PREFIXES=admin,root,support
SESSION_SECRET=a_very_secret_key_that_should_be_changed
```

#### 使用 config.json 配置

或者，您可以直接修改根目录下的 `config.json` 文件：

```json
{
  "PORT": 80,           // Web服务端口
  "SMTP_PORT": 25,      // SMTP服务端口
  "SMTP_HOST": "0.0.0.0", // SMTP监听地址
  "MAX_MAILS": 50,      // 每个邮箱最多保留邮件数
  "MAIL_EXPIRE_MINUTES": 10, // 邮件保留时长（分钟）
  "ADMIN_USER": "admin",     // 管理员用户名
  "ADMIN_PASSWORD": "password", // 管理员密码
  "FORBIDDEN_PREFIXES": ["admin", "root", "support"], // 禁用的邮箱前缀列表
  "SESSION_SECRET": "a_very_secret_key_that_should_be_changed" // 会话密钥
}
```

> **注意**：如果同时存在 .env 和 config.json 文件，.env 中的配置会覆盖 config.json 中的同名配置。

> **安全提示**：在生产环境中，请务必修改默认的管理员用户名和密码，并设置一个强密钥作为会话密钥。

### 4. 启动服务

```bash
# 直接启动
npm start

# 或使用PM2管理（推荐生产环境）
npm install -g pm2
pm2 start src/server.js --name meteormail
```

### 5. 访问服务

在浏览器中访问：
```
http://localhost
```
或者使用您在配置文件中设置的端口（默认为80）：
```
http://localhost:80
```
- 页面自动分配临时邮箱ID，可实时接收邮件
- 支持刷新ID、自定义ID、复制邮箱地址、手动删除邮件
- 访问 `/about.html` 查看项目介绍和技术架构

## 管理后台

### 访问管理后台

浏览器访问：
```
http://localhost/login.html
```

使用您在配置文件中设置的管理员用户名和密码登录。

### 管理后台功能

登录后，您可以：

1. **系统设置**
   - 调整邮件保留时间（分钟）
   - 设置每个邮箱最大保留邮件数量

2. **安全设置**
   - 管理禁用的邮箱前缀列表
   - 每行输入一个前缀，如 `admin`、`root`、`support` 等

3. **界面设置**
   - 切换语言（中文/英文）
   - 切换主题（深色/浅色模式）

### 安全建议

1. **修改默认凭据**：务必修改默认的管理员用户名和密码
2. **设置强会话密钥**：修改 `SESSION_SECRET` 为一个复杂的随机字符串
3. **禁用敏感前缀**：添加可能被滥用的邮箱前缀到禁用列表中
4. **限制管理页面访问**：考虑使用反向代理（如 Nginx）添加额外的访问控制

## 解决常见问题

### 端口权限问题

在Linux系统中，使用低于1024的端口（如25、80）需要root权限：

```bash
# 使用sudo启动（不推荐生产环境）
sudo npm start

# 更好的方法是使用以下命令授予Node.js绑定特权端口的能力：
sudo setcap 'cap_net_bind_service=+ep' `which node`
```

### CSP安全策略警告

如果遇到内容安全策略(CSP)相关的警告，解决方法：

1. 修改 src/app.js 中的 Helmet 配置，禁用CSP检查：
```javascript
app.use(helmet({
  contentSecurityPolicy: false  // 完全禁用CSP检查
}));
```

2. 确保前端脚本正确加载：
```html
<!-- 使用动态脚本加载方式 -->
<script>
  const baseUrl = window.location.origin;
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = baseUrl + '/' + src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  loadScript('socket.io.min.js')
    .then(() => loadScript('clipboard.min.js'))
    .then(() => loadScript('app.js'))
    .catch(err => console.error('脚本加载失败:', err));
</script>
```

### WebSocket连接问题

如果WebSocket连接失败，请尝试以下方法：

1. 修改 public/app.js 的连接方式：
```javascript
// 使用当前窗口的location作为Socket.IO连接地址
const socket = io(window.location.origin);
```

2. 使用诊断页面检测：访问 `/diagnostic.html` 检查连接信息

### 4.5 管理后台登录问题

- **症状**: 无法登录管理后台或登录后页面显示错误
- **解决方案**:
  1. 确认配置文件中的管理员用户名和密码设置正确
  2. 检查浏览器控制台是否有错误信息
  3. 确保 `SESSION_SECRET` 已正确设置
  4. 尝试清除浏览器缓存和 Cookie

### 4.6 禁用前缀功能不生效

- **症状**: 设置了禁用前缀，但仍然可以创建使用这些前缀的邮箱
- **解决方案**:
  1. 确认配置文件中的 `FORBIDDEN_PREFIXES` 格式正确
  2. 检查服务器日志是否有相关错误
  3. 重启服务以确保新配置生效
  4. 确保前端和API都受到了前缀限制的保护

## 生产环境部署建议

### Docker部署

使用Docker可以快速部署MeteorMail服务，无需手动配置环境。以下提供三种Docker部署方式，您可以根据需求选择最适合的方式。

#### 方式1：使用docker-compose部署（推荐）

1. 首先准备Dockerfile（项目已包含）:

```dockerfile
FROM node:16-alpine

WORKDIR /app

# 先复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制其余项目文件
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 80 25

# 启动服务
CMD ["node", "src/server.js"]
```

2. 准备docker-compose.yml文件（项目已包含）:

```yaml
version: '3'

services:
  meteormail:
    build: .
    container_name: meteormail
    restart: always
    ports:
      - "80:80"
      - "25:25"
    volumes:
      - ./config.json:/app/config.json
      - ./.env:/app/.env
      - ./logs:/app/logs
```

3. 在启动前确保配置正确:

修改根目录下的配置文件，确保端口设置符合你的需求:

对于 `.env` 文件:
```
PORT=80
SMTP_PORT=25
SMTP_HOST=0.0.0.0
MAX_MAILS=50
MAIL_EXPIRE_MINUTES=10
```

4. 构建并运行Docker镜像:

```bash
# 进入项目目录
cd MeteorMail

# 构建镜像并启动服务
docker-compose up -d

# 查看容器状态
docker ps

# 查看日志
docker logs -f meteormail
```

#### 方式2：手动构建镜像并部署

如果您不想使用docker-compose，也可以手动构建镜像并运行容器：

```bash
# 进入项目目录
cd MeteorMail

# 构建镜像
docker build -t meteormail:latest .

# 运行容器
docker run -d \
  --name meteormail \
  --restart=unless-stopped \
  -p 80:80 \
  -p 25:25 \
  -v $(pwd)/config.json:/app/config.json \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/logs:/app/logs \
  meteormail:latest
```

#### 方式3：一键运行（无需克隆仓库）

您可以使用官方 Docker 镜像一键部署 MeteorMail：

##### 最简单的一键部署命令

```bash
# 最简单的一键部署（使用默认配置）
docker run -d --name meteormail --restart=unless-stopped -p 80:80 -p 25:25 lbjlaq/meteormail:latest
```

```bash
# 宝塔 docker 一键部署 （使用 3000 端口）
docker run -d --name meteormail --restart=unless-stopped -p 3000:80 -p 25:25 lbjlaq/meteormail:latest
```

这个命令会使用镜像中的默认配置，适合快速测试或简单使用场景。

##### 带持久化存储和自定义配置的部署命令

如果需要持久化存储和自定义配置，可以使用以下命令：

```bash
# 创建配置目录
mkdir -p ~/meteormail/{config,logs}

# 创建配置文件
cat > ~/meteormail/config/config.json << EOF
{
  "PORT": 80,
  "SMTP_PORT": 25,
  "SMTP_HOST": "0.0.0.0",
  "MAX_MAILS": 50,
  "MAIL_EXPIRE_MINUTES": 10,
  "ADMIN_USER": "admin",
  "ADMIN_PASSWORD": "your_secure_password",
  "FORBIDDEN_PREFIXES": ["admin", "root", "support"],
  "SESSION_SECRET": "your_very_secret_key_here"
}
EOF

# 运行容器
docker run -d \
  --name meteormail \
  --restart=unless-stopped \
  -p 80:80 \
  -p 25:25 \
  -v ~/meteormail/config:/app/config \
  -v ~/meteormail/logs:/app/logs \
  -e PORT=80 \
  -e ADMIN_USER=admin \
  -e ADMIN_PASSWORD=your_secure_password \
  lbjlaq/meteormail:latest
```

官方镜像已发布在 Docker Hub：[lbjlaq/meteormail](https://hub.docker.com/r/lbjlaq/meteormail)，支持 ARM64 和 AMD64 多平台架构。

#### Docker配置说明

- **端口映射**：默认映射主机的80和25端口到容器
- **配置文件**：通过卷挂载方式映射到容器内
- **日志目录**：持久化存储到主机
- **环境变量**：可以通过`-e`参数设置环境变量，覆盖配置文件中的设置
- **重启策略**：`--restart=unless-stopped`确保容器在异常退出或系统重启后自动重启

#### 端口调整

如果服务器上的80或25端口已被占用，可以修改端口映射：

```yaml
# docker-compose.yml
ports:
  - "8080:80"  # 将容器内的80端口映射到主机的8080端口
  - "2525:25"  # 将SMTP端口映射到2525
```

或者使用`docker run`命令：

```bash
docker run -d \
  --name meteormail \
  --restart=unless-stopped \
  -p 8080:80 \
  -p 2525:25 \
  lbjlaq/meteormail:latest
```

#### 服务管理命令

```bash
# 停止服务
docker stop meteormail

# 启动服务
docker start meteormail

# 重启服务
docker restart meteormail

# 查看日志
docker logs -f meteormail

# 删除容器
docker rm -f meteormail

# 更新镜像（如果使用Docker Hub镜像）
docker pull lbjlaq/meteormail:latest
docker stop meteormail
docker rm meteormail
# 然后重新运行上面的docker run命令
```

#### Docker部署的优势

- **环境隔离**：不会影响宿主机的其他服务
- **一键部署**：无需手动安装Node.js和依赖
- **自动重启**：服务器重启后容器会自动启动
- **方便迁移**：可以轻松在不同服务器之间迁移
- **版本控制**：可以使用不同版本的镜像，方便回滚
- **资源限制**：可以限制容器使用的CPU和内存资源

### 配置HTTPS

```bash
# 安装certbot（以Ubuntu为例）
apt update
apt install certbot 

# 获取并配置证书
certbot certonly --standalone -d mail.yourdomain.com
```

然后修改配置，使用HTTPS：

```javascript
// 在src/server.js中
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('/etc/letsencrypt/live/mail.yourdomain.com/privkey.pem'),
  cert: fs.readFileSync('/etc/letsencrypt/live/mail.yourdomain.com/fullchain.pem')
};

const server = https.createServer(options, app);
// 其余代码保持不变
```

### 使用PM2持久化运行

```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start src/server.js --name meteormail

# 查看运行状态
pm2 status

# 查看日志
pm2 logs meteormail

# 设置开机自启
pm2 startup
pm2 save
```

## 测试邮件发送

### 使用 SMTP 服务测试

您可以使用任何支持 SMTP 协议的客户端或工具发送邮件到您的临时邮箱。以下是一些测试方法：

### 1. 使用命令行工具 swaks 发送测试邮件
```bash
swaks --to <邮箱ID>@localhost --server 127.0.0.1:25 --from <EMAIL> --header "Subject: 测试邮件" --body "你好，这是一封测试邮件"
```

### 2. 使用 telnet 手动测试 SMTP 协议
```bash
telnet localhost 25
```
依次输入：
```
HELO localhost
MAIL FROM:<<EMAIL>>
RCPT TO:<邮箱ID@localhost>
DATA
Subject: 测试邮件

你好，这是一封测试邮件
.
QUIT
```

### 3. 使用邮件客户端

您也可以配置邮件客户端（如 Thunderbird、Outlook 等）发送邮件：
- SMTP 服务器：localhost 或您的服务器IP
- 端口：25
- 不需要身份验证
- 收件人：<邮箱ID>@localhost 或 <邮箱ID>@您的域名

## 持久化运行

服务正常启动后，你会看到如下输出：

```
> fm@1.0.0 start
> node src/server.js
服务已启动，端口: 80
SMTP服务已启动，端口: 25, 地址: 0.0.0.0
```

为了让服务持续在后台运行，有以下几种方法：

### 1. 使用PM2（推荐）

PM2是Node.js应用的进程管理工具，可以让应用在后台持续运行，并支持开机自启：

```bash
# 安装PM2
npm install -g pm2

# 启动Forsaken Mail
cd MeteorMail
pm2 start src/server.js --name meteormail

# 查看运行状态
pm2 status

# 查看日志
pm2 logs meteormail

# 设置开机自启
pm2 startup
pm2 save
```

PM2的优势：
- 自动重启：如果应用崩溃会自动重启
- 日志管理：自动保存输出日志
- 性能监控：可以监控CPU和内存使用情况
- 负载均衡：可以启动多个实例进行负载均衡

### 2. 使用systemd服务（Linux系统）

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/meteormail.service
```

填入以下内容：

```
[Unit]
Description=MeteorMail Temporary Email Service
After=network.target

[Service]
Type=simple
User=你的用户名
WorkingDirectory=/path/to/MeteorMail
ExecStart=/usr/bin/node /path/to/MeteorMail/src/server.js
Restart=on-failure
# 环境变量可以在配置文件.env或config.json中设置，这里不需要指定
# 如果仍需要在这里指定，可以添加：
# Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

然后启用并启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable meteormail
sudo systemctl start meteormail

# 查看状态
sudo systemctl status meteormail

# 查看日志
sudo journalctl -u meteormail -f
```

### 3. 使用screen或tmux（简单方法）

如果没有root权限或想要简单实现：

```bash
# 安装screen
sudo apt install screen  # Ubuntu/Debian
sudo yum install screen  # CentOS

# 创建新会话
screen -S meteormail

# 进入项目目录并启动
cd MeteorMail
npm start

# 按Ctrl+A然后按D，将会话分离到后台
# 要重新连接会话：
screen -r meteormail
```

### 4. 使用nohup（最简单方法）

```bash
cd MeteorMail
nohup npm start > fm.log 2>&1 &

# 获取进程ID
echo $! > fm.pid

# 要停止服务
kill $(cat fm.pid)
```

### 5. 使用Docker持久化

如果使用Docker部署：

```bash
# 使用--restart=always参数确保容器自动重启
docker run -d --name meteormail --restart=always -p 80:80 -p 25:25 -v $(pwd)/config.json:/app/config.json -v $(pwd)/.env:/app/.env -v $(pwd)/logs:/app/logs meteormail

# 或者使用docker-compose (推荐)
# 在docker-compose.yml中添加restart: always配置
version: '3'

services:
  meteormail:
    build: .
    container_name: meteormail
    restart: always   # 确保自动重启
    ports:
      - "80:80"
      - "25:25"
    volumes:
      - ./config.json:/app/config.json
      - ./.env:/app/.env
      - ./logs:/app/logs
```

#### 域名配置

您可以通过多种方式将域名指向您的 MeteorMail 服务：

##### 1. 使用 A 记录

直接将域名指向服务器 IP 地址：

```
mail.yourdomain.com.  IN  A  123.456.789.10  # 您的服务器IP
```

配置步骤：
1. 登录您的域名注册商或 DNS 提供商的控制面板
2. 找到 DNS 记录管理页面
3. 添加一条 A 记录，将子域名（如 mail）指向您的服务器 IP 地址
4. 保存更改，等待 DNS 记录生效（通常需要几分钟到几小时）

##### 2. 使用 CNAME 记录

将域名指向另一个域名：

```
mail.yourdomain.com.  IN  CNAME  your-server.example.com.
```

配置步骤：
1. 登录您的域名注册商或 DNS 提供商的控制面板
2. 找到 DNS 记录管理页面
3. 添加一条 CNAME 记录，将子域名（如 mail）指向您的服务器域名
4. 保存更改，等待 DNS 记录生效（通常需要几分钟到几小时）

推荐使用PM2或Docker，这些工具提供了完善的进程管理功能，能够在系统重启后自动恢复服务，并且有日志管理和监控功能。

## 注意事项

1. **安全考虑**：临时邮箱可能被用于接收敏感信息，建议限制访问权限
2. **防止滥用**：考虑添加访问限制或验证码，避免被滥用
3. **定期维护**：监控服务器资源占用情况，设置日志轮转
4. **SMTP端口保护**：配置防火墙规则，限制SMTP端口访问

## 常用API

### 获取邮箱邮件列表
```
GET /api/mails/:mailboxAddr
```

### 获取指定邮件
```
GET /api/mails/:mailboxAddr/:idx
```

### 删除指定邮件
```
DELETE /api/mails/:mailboxAddr/:idx
```

## 故障排查

如果服务无法正常工作，请按照以下步骤进行故障排查：

### 1. 检查Node.js环境

```bash
# 检查Node.js版本
node -v  # 应至少为v16.x.x

# 检查npm版本
npm -v

# 检查项目依赖是否完整
cd MeteorMail
npm list --depth=0
```

### 2. 检查网络和端口状态

```bash
# 检查端口占用情况
sudo netstat -tuln | grep -E ':(80|25)'  # Linux
lsof -i :80 -i :25  # macOS

# 测试SMTP端口连通性
telnet localhost 25
# 如果能连接，应该显示类似"Connected to localhost"

# 检查防火墙设置(Linux)
sudo iptables -L | grep -E '(80|25)'
```

### 3. 查看服务日志

```bash
# 直接启动并查看输出
node src/server.js

# 如果使用PM2
pm2 logs meteormail

# 如果使用Docker
docker logs -f meteormail
```

### 4. 常见错误和解决方案

#### 4.1 EADDRINUSE错误

```
Error: listen EADDRINUSE: address already in use :::80
```

**解决方案:**
- 找出占用端口的进程并停止它: `fuser -k 80/tcp` (Linux) 或 `kill $(lsof -ti:80)` (macOS)
- 或修改配置使用其他端口: 编辑`.env`或`config.json`将PORT改为其他值(如8080)

#### 4.2 EACCES错误(权限不足)

```
Error: listen EACCES: permission denied 0.0.0.0:25
```

**解决方案:**
- 使用sudo运行: `sudo npm start`
- 或修改配置使用1024以上的端口: 编辑`.env`或`config.json`将SMTP_PORT改为2525
- 或使用authbind允许非root用户绑定特权端口: `authbind --deep npm start`

#### 4.3 前端加载问题

- **症状**: 页面加载但Socket.IO不工作，控制台有CSP警告
- **解决方案**: 按照上面的"CSP安全策略警告"部分修改app.js和index.html

#### 4.4 邮件发送测试不成功

- **症状**: 使用Python脚本发送邮件但前端没收到
- **排查方法**:
  1. 检查SMTP服务是否启动: `telnet localhost 25`
  2. 检查服务器日志是否有接收邮件的记录
  3. 确认邮件地址格式正确: `<邮箱ID>@服务器IP地址`
  4. 使用curl检查API: `curl http://localhost/api/mails/邮箱ID%40服务器地址`

### 5. 使用诊断页面

访问 `/diagnostic.html` 页面，该页面可以:
- 显示当前浏览器和连接信息
- 测试Socket.IO连接
- 帮助诊断前端问题

### 6. 获取更多帮助

如果以上方法都无法解决问题:
1. 在GitHub仓库提交issue: https://github.com/lbjlaq/MeteorMail/issues
2. 附上以下信息:
   - 操作系统和版本
   - Node.js和npm版本
   - 完整的错误信息和日志
   - 搭建步骤和配置修改

如有其他问题，请提交issue到GitHub仓库。